import React, { useState, useEffect } from 'react';
import { safeIpcInvoke } from '../utils/electron';
import { Modal } from '../components/modal';
import { Button } from '../components/button';

interface BankMaster {
  ftbnkid?: number;       // Auto-incrementing ID (primary key)
  ftbnkcode: string;      // Bank code (unique, max 5 chars)
  ftbnkname: string;      // Bank name (max 100 chars)
  ftstaactive: string;    // Status active (1=Active, 0=Inactive)
  fddateupd?: string;     // Date updated (TIMESTAMP)
  fttimeupd?: string;     // Time updated (VARCHAR 8)
  ftwhoupd?: string;      // Who updated (VARCHAR 30)
  fddateins?: string;     // Date inserted (TIMESTAMP)
  fttimeins?: string;     // Time inserted (VARCHAR 8)
  ftwhoins?: string;      // Who inserted (VARCHAR 30)
}

interface BankMasterResponse {
  success: boolean;
  message: string;
  data?: BankMaster | BankMaster[];
  error?: string;
}

export function BankMasterScreen() {
  const [banks, setBanks] = useState<BankMaster[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBank, setEditingBank] = useState<BankMaster | null>(null);
  const [formData, setFormData] = useState<Omit<BankMaster, 'ftbnkid' | 'fddateins' | 'fttimeins'>>({
    ftbnkcode: '',
    ftbnkname: '',
    ftstaactive: '1',
    ftwhoins: 'SYSTEM'
  });

  // Load banks on component mount
  useEffect(() => {
    initializeTable();
    loadBanks();
  }, []);

  const initializeTable = async () => {
    try {
      await safeIpcInvoke('init-bank-table');
    } catch (error) {
      console.error('Error initializing table:', error);
    }
  };

  const loadBanks = async () => {
    setIsLoading(true);
    try {
      const response: BankMasterResponse = await safeIpcInvoke('get-banks');
      if (response.success && Array.isArray(response.data)) {
        setBanks(response.data);
      } else {
        console.error('Failed to load banks:', response.message);
      }
    } catch (error) {
      console.error('Error loading banks:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingBank(null);
    setFormData({
      ftbnkcode: '',
      ftbnkname: '',
      ftstaactive: '1',
      ftwhoins: 'SYSTEM'
    });
    setIsModalOpen(true);
  };

  const handleEdit = (bank: BankMaster) => {
    setEditingBank(bank);
    setFormData({
      ftbnkcode: bank.ftbnkcode,
      ftbnkname: bank.ftbnkname,
      ftstaactive: bank.ftstaactive,
      ftwhoupd: 'SYSTEM'
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (bank: BankMaster) => {
    if (!confirm(`Are you sure you want to delete ${bank.ftbnkname}?`)) {
      return;
    }

    if (!bank.ftbnkid) {
      alert('Cannot delete bank: Invalid bank ID');
      return;
    }

    try {
      const response: BankMasterResponse = await safeIpcInvoke('delete-bank', bank.ftbnkid);
      if (response.success) {
        await loadBanks(); // Reload the list
        alert('Bank deleted successfully');
      } else {
        alert(`Failed to delete bank: ${response.message}`);
      }
    } catch (error) {
      console.error('Error deleting bank:', error);
      alert('Error deleting bank');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.ftbnkcode.trim() || !formData.ftbnkname.trim()) {
      alert('Bank code and name are required');
      return;
    }

    if (formData.ftbnkcode.length > 5) {
      alert('Bank code must be 5 characters or less');
      return;
    }

    if (formData.ftbnkname.length > 100) {
      alert('Bank name must be 100 characters or less');
      return;
    }

    try {
      let response: BankMasterResponse;

      if (editingBank && editingBank.ftbnkid) {
        // Update existing bank
        response = await safeIpcInvoke('update-bank', editingBank.ftbnkid, formData);
      } else {
        // Create new bank
        response = await safeIpcInvoke('create-bank', formData);
      }

      if (response.success) {
        setIsModalOpen(false);
        await loadBanks(); // Reload the list
        alert(editingBank ? 'Bank updated successfully' : 'Bank created successfully');
      } else {
        alert(`Failed to ${editingBank ? 'update' : 'create'} bank: ${response.message}`);
      }
    } catch (error) {
      console.error('Error saving bank:', error);
      alert('Error saving bank');
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '-';
    return new Date(dateStr).toLocaleDateString();
  };

  const formatTime = (timeStr?: string) => {
    if (!timeStr) return '-';
    return timeStr;
  };

  console.log("formData",formData)

  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Bank Master Management</h1>
          <Button
            onClick={handleCreate}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            + Add New Bank
          </Button>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600">Loading banks...</span>
          </div>
        )}

        {/* Banks Table */}
        {!isLoading && (
          <div className="overflow-x-auto">
            <table className="min-w-full table-auto border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-4 py-2 text-center">ID</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Bank Code</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Bank Name</th>
                  <th className="border border-gray-300 px-4 py-2 text-center">Status</th>
                  <th className="border border-gray-300 px-4 py-2 text-center">Created Date</th>
                  <th className="border border-gray-300 px-4 py-2 text-center">Updated Date</th>
                  <th className="border border-gray-300 px-4 py-2 text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {banks.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="border border-gray-300 px-4 py-8 text-center text-gray-500">
                      No banks found. Click "Add New Bank" to create one.
                    </td>
                  </tr>
                ) : (
                  banks.map((bank) => (
                    <tr key={bank.ftbnkid || bank.ftbnkcode} className="hover:bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2 text-center font-mono text-sm">{bank.ftbnkid}</td>
                      <td className="border border-gray-300 px-4 py-2 font-mono">{bank.ftbnkcode}</td>
                      <td className="border border-gray-300 px-4 py-2">{bank.ftbnkname}</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          bank.ftstaactive === '1'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {bank.ftstaactive === '1' ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-center text-sm">
                        {formatDate(bank.fddateins)}
                        {bank.fttimeins && <br />}
                        <span className="text-gray-500">{formatTime(bank.fttimeins)}</span>
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-center text-sm">
                        {formatDate(bank.fddateupd)}
                        {bank.fttimeupd && <br />}
                        <span className="text-gray-500">{formatTime(bank.fttimeupd)}</span>
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-center">
                        <div className="flex gap-2 justify-center">
                          <Button
                            onClick={() => handleEdit(bank)}
                            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                          >
                            Edit
                          </Button>
                          <Button
                            onClick={() => handleDelete(bank)}
                            className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Summary */}
        {!isLoading && (
          <div className="mt-4 text-sm text-gray-600">
            Total Banks: {banks.length} |
            Active: {banks.filter(b => b.ftstaactive === '1').length} |
            Inactive: {banks.filter(b => b.ftstaactive === '0').length}
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingBank ? 'Edit Bank' : 'Create New Bank'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bank Code <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.ftbnkcode}
              onChange={(e) => handleInputChange('ftbnkcode', e.target.value.toUpperCase())}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., BBL, SCB, KTB"
              maxLength={5}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bank Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.ftbnkname}
              onChange={(e) => handleInputChange('ftbnkname', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Bangkok Bank, Siam Commercial Bank"
              maxLength={100}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={formData.ftstaactive.toString()}
              onChange={(e) => handleInputChange('ftstaactive', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="1">Active</option>
              <option value="0">Inactive</option>
            </select>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              {editingBank ? 'Update Bank' : 'Create Bank'}
            </Button>
            <Button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="flex-1 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Cancel
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
}
