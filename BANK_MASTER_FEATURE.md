# Bank Master Management Feature

This document describes the Bank Master management functionality that has been added to the Electron application.

## Overview

The Bank Master management feature provides a complete CRUD (Create, Read, Update, Delete) interface for managing bank master data in the `tmst_bank` table. This feature is designed to work with your existing database schema and provides a user-friendly interface for bank data management.

## Features

### 🏦 Bank Master Management
- **Create**: Add new banks with bank code and name
- **Read**: View all banks in a sortable table format
- **Update**: Edit existing bank information
- **Delete**: Remove banks with confirmation
- **Status Management**: Toggle active/inactive status

### 📊 Table Features
- Responsive table layout with proper column headers
- Status indicators (Active/Inactive badges)
- Date and time formatting for audit fields
- Summary statistics (Total, Active, Inactive counts)
- Empty state handling

### 🎨 User Interface
- Modal-based create/edit forms
- Form validation for required fields
- Confirmation dialogs for delete operations
- Loading states and error handling
- Responsive design for different screen sizes

## Database Schema

The feature works with the existing `tmst_bank` table structure:

```sql
CREATE TABLE tmst_bank (
  ftbnkcode VARCHAR(10) PRIMARY KEY,    -- Bank code (unique identifier)
  ftbnkname VARCHAR(255) NOT NULL,      -- Bank name
  ftstaactive CHAR(1) DEFAULT '1',      -- Status (1=Active, 0=Inactive)
  fddateupd DATE,                       -- Date updated
  fttimeupd TIME,                       -- Time updated
  ftwhoupd VARCHAR(50),                 -- Who updated
  fddateins DATE DEFAULT CURRENT_DATE,  -- Date inserted
  fttimeins TIME DEFAULT CURRENT_TIME,  -- Time inserted
  ftwhoins VARCHAR(50)                  -- Who inserted
);
```

## Usage

### Accessing the Feature
1. Start the Electron application: `npm run dev`
2. Navigate to the "Bank Master" tab in the navigation bar
3. The table will automatically initialize if it doesn't exist

### Managing Banks

#### Creating a New Bank
1. Click the "Add New Bank" button
2. Fill in the required fields:
   - **Bank Code**: Unique identifier (e.g., BBL, SCB, KTB)
   - **Bank Name**: Full bank name (e.g., Bangkok Bank)
   - **Status**: Active or Inactive
3. Click "Create Bank" to save

#### Editing an Existing Bank
1. Click the "Edit" button next to the bank you want to modify
2. Update the bank name or status (bank code cannot be changed)
3. Click "Update Bank" to save changes

#### Deleting a Bank
1. Click the "Delete" button next to the bank you want to remove
2. Confirm the deletion in the popup dialog
3. The bank will be permanently removed from the database

### Field Descriptions

- **Bank Code**: Unique identifier for the bank (max 10 characters, uppercase)
- **Bank Name**: Full name of the bank (max 255 characters)
- **Status**:
  - `1` = Active (bank is currently in use)
  - `0` = Inactive (bank is disabled)
- **Created Date/Time**: Automatically set when bank is first created
- **Updated Date/Time**: Automatically updated when bank is modified
- **Who Created/Updated**: User who performed the action (defaults to "SYSTEM")

## Technical Implementation

### Architecture
The feature follows the existing IPC (Inter-Process Communication) pattern:

1. **Frontend (Renderer Process)**: `src/renderer/screens/bank-master.screen.tsx`
   - React component with table and modal interfaces
   - Uses `safeIpcInvoke` to communicate with main process

2. **Backend (Main Process)**: `src/main/ipcHandlers.ts`
   - IPC handlers for CRUD operations
   - PostgreSQL database connection and queries
   - Error handling and validation

### IPC Handlers

- `init-bank-table`: Initialize/check table existence
- `get-banks`: Retrieve all banks
- `create-bank`: Create a new bank
- `update-bank`: Update existing bank
- `delete-bank`: Delete a bank

### Database Connection
Uses the existing PostgreSQL connection configuration:
- **Host**: ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech
- **Database**: neondb
- **User**: neondb_owner
- **SSL**: Enabled

### Error Handling
Comprehensive error handling for:
- Database connection issues
- Unique constraint violations (duplicate bank codes)
- Missing required fields
- Bank not found scenarios
- Network connectivity problems

## Files Modified/Added

### New Files
- `src/renderer/screens/bank-master.screen.tsx` - Main UI component
- `BANK_MASTER_FEATURE.md` - This documentation

### Modified Files
- `src/main/ipcHandlers.ts` - Added bank master CRUD handlers
- `src/renderer/routes.tsx` - Added new route
- `src/renderer/layout.tsx` - Added navigation link

## Security Considerations

- SQL injection prevention through parameterized queries
- Input validation and sanitization
- Confirmation dialogs for destructive operations
- Audit trail with creation/update timestamps and user tracking

## Sample Data

Example banks you might add:

| Bank Code | Bank Name | Status |
|-----------|-----------|---------|
| BBL | Bangkok Bank | Active |
| SCB | Siam Commercial Bank | Active |
| KTB | Krung Thai Bank | Active |
| TMB | TMB Bank | Active |
| BAY | Bank of Ayudhya | Active |
| GSB | Government Savings Bank | Active |
| KBANK | Kasikorn Bank | Active |

## Future Enhancements

Potential improvements for this feature:
- Bulk import/export functionality
- Advanced search and filtering
- Bank branch management
- Integration with other banking modules
- Data validation rules
- Backup and restore functionality
- User permission management
- Audit log viewing

## Troubleshooting

### Common Issues

1. **Table Not Found**
   - The application automatically creates the table if it doesn't exist
   - Check database connection settings
   - Verify user has CREATE TABLE permissions

2. **Duplicate Bank Code**
   - Bank codes must be unique
   - Use a different code or update the existing bank

3. **Connection Errors**
   - Verify PostgreSQL connection settings
   - Check network connectivity
   - Ensure database server is running

4. **Permission Errors**
   - Verify database user has INSERT, UPDATE, DELETE permissions
   - Check if user can access the specific database

## Support

For issues or questions about this feature:
1. Check the console logs in the Electron DevTools
2. Review the error messages displayed in the UI
3. Verify the database connection using the PostgreSQL Test feature
4. Check the database table structure matches the expected schema

## Integration Notes

This Bank Master feature is designed to integrate with other modules that might need bank information:
- Payment processing systems
- Account management
- Financial reporting
- Transaction processing

The standardized bank codes can be used as foreign keys in other tables that reference bank information.
